# ============================================================================
# LangGraph多智能体AI旅行规划系统 - 环境变量配置文件
# ============================================================================
#
# 这个文件定义了系统运行所需的所有环境变量和API密钥。
# 环境变量是一种安全的方式来存储敏感信息（如API密钥），
# 避免将这些信息直接写在代码中。
#
# 适用于大模型技术初级用户：
# - 环境变量是现代软件开发的标准实践
# - 它们允许在不修改代码的情况下配置应用程序
# - 这种方式提高了安全性和部署的灵活性
# ============================================================================

# ----------------------------------------------------------------------------
# 🚀 核心API配置 (LangGraph系统必需)
# ----------------------------------------------------------------------------

# Google Gemini API密钥 (必需 - LangGraph系统的核心)
# 功能说明：
# - Gemini是Google开发的先进大语言模型
# - 用于驱动所有AI智能体的推理和决策
# - 支持多模态输入和高质量的文本生成
# - 获取地址：https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_google_gemini_api_key_here

# ----------------------------------------------------------------------------
# 🔧 传统系统API配置 (可选 - 用于兼容原始系统)
# ----------------------------------------------------------------------------

# OpenWeather API密钥 (可选)
# 功能说明：
# - 提供全球天气数据和预报信息
# - 用于传统天气服务模块
# - 新LangGraph系统使用DuckDuckGo搜索获取天气信息
# - 获取地址：https://openweathermap.org/api
OPENWEATHER_API_KEY=your_openweather_api_key_here

# Google Places API密钥 (可选)
# 功能说明：
# - 提供地点搜索、详细信息和评价数据
# - 用于传统景点和酒店查找模块
# - 新LangGraph系统使用实时搜索替代
# - 获取地址：https://developers.google.com/maps/documentation/places/web-service
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

# ExchangeRate API密钥 (可选)
# 功能说明：
# - 提供实时汇率数据
# - 用于货币转换功能
# - 支持多种国际货币
# - 获取地址：https://exchangerate-api.com/
EXCHANGERATE_API_KEY=your_exchange_rate_api_key_here

# ============================================================================
# 📋 配置说明和使用指南
# ============================================================================
#
# 🎯 快速开始 (仅需1步)：
# 1. 将此文件复制为 .env
# 2. 将 GEMINI_API_KEY 替换为您的实际API密钥
# 3. 运行系统：python langgraph_main.py
#
# 🔍 详细配置说明：
#
# 1. 📁 文件复制：
#    cp .env.example .env
#    (将示例文件复制为实际配置文件)
#
# 2. 🔑 获取Gemini API密钥：
#    - 访问：https://makersuite.google.com/app/apikey
#    - 使用Google账号登录
#    - 创建新的API密钥
#    - 复制密钥并替换下面的占位符
#
# 3. 🚀 系统选择：
#    - 新用户推荐：LangGraph系统 (仅需GEMINI_API_KEY)
#    - 完整体验：配置所有API密钥
#    - 测试模式：使用模拟数据 (无需API密钥)
#
# 4. 🔒 安全注意事项：
#    - 永远不要将 .env 文件提交到版本控制系统
#    - 定期更换API密钥
#    - 不要在公共场所分享API密钥
#    - 监控API使用量和费用
#
# 5. 🛠️ 故障排除：
#    - 确保 .env 文件在项目根目录
#    - 检查API密钥格式是否正确
#    - 验证API密钥是否有效且未过期
#    - 确认API配额是否充足
#
# ============================================================================
# 🌟 系统功能对比
# ============================================================================
#
# LangGraph系统 (推荐)：
# ✅ 仅需 GEMINI_API_KEY
# ✅ 实时搜索集成 (DuckDuckGo)
# ✅ 多智能体协作
# ✅ 高级AI推理能力
# ✅ 免费搜索功能
#
# 传统系统：
# 🔧 需要多个API密钥
# 🔧 静态数据源
# 🔧 单一智能体
# 🔧 基础功能
# 🔧 API费用较高
#
# ============================================================================
