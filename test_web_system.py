#!/usr/bin/env python3
"""
LangGraph多智能体AI旅行规划系统 - Web版本测试脚本

这个脚本用于测试Web版本的API接口和功能。
"""

import requests
import json
import time
from datetime import datetime, date, timedelta

API_BASE_URL = "http://localhost:8000"

def test_api_health():
    """测试API健康状态"""
    print("🔍 测试API健康状态...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ API服务正常运行")
            print(f"   状态: {health_data.get('status')}")
            print(f"   模型: {health_data.get('gemini_model')}")
            print(f"   API密钥: {'已配置' if health_data.get('api_key_configured') else '未配置'}")
            return True
        else:
            print(f"❌ API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到API服务: {str(e)}")
        print("请确保后端服务正在运行: ./start_backend.sh")
        return False

def test_create_travel_plan():
    """测试创建旅行规划"""
    print("\n🚀 测试创建旅行规划...")
    
    # 创建测试数据
    travel_data = {
        "destination": "北京",
        "start_date": (date.today() + timedelta(days=7)).strftime("%Y-%m-%d"),
        "end_date": (date.today() + timedelta(days=14)).strftime("%Y-%m-%d"),
        "budget_range": "中等预算",
        "group_size": 2,
        "interests": ["历史", "美食", "文化"],
        "dietary_restrictions": "",
        "activity_level": "适中",
        "travel_style": "探索者",
        "transportation_preference": "公共交通",
        "accommodation_preference": "酒店",
        "special_requirements": "",
        "currency": "CNY"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/plan", json=travel_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            task_id = result["task_id"]
            print(f"✅ 规划任务创建成功")
            print(f"   任务ID: {task_id}")
            print(f"   状态: {result['status']}")
            return task_id
        else:
            print(f"❌ 创建任务失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return None

def test_planning_status(task_id):
    """测试规划状态查询"""
    print(f"\n🔄 监控规划进度 (任务ID: {task_id})")
    
    max_attempts = 60  # 最多等待1分钟
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{API_BASE_URL}/status/{task_id}", timeout=5)
            if response.status_code == 200:
                status = response.json()
                
                print(f"\r进度: {status['progress']}% | 状态: {status['status']} | {status['message']}", end="")
                
                if status['status'] == 'completed':
                    print(f"\n✅ 规划完成！")
                    return status
                elif status['status'] == 'failed':
                    print(f"\n❌ 规划失败: {status['message']}")
                    return None
                    
            time.sleep(1)
            attempt += 1
            
        except Exception as e:
            print(f"\n❌ 状态查询失败: {str(e)}")
            return None
    
    print(f"\n⏰ 规划超时")
    return None

def test_download_result(task_id):
    """测试结果下载"""
    print(f"\n📥 测试结果下载...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/download/{task_id}", timeout=10)
        if response.status_code == 200:
            # 保存文件
            filename = f"test_result_{task_id[:8]}.json"
            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f"✅ 结果已下载到: {filename}")
            return True
        else:
            print(f"❌ 下载失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 下载请求失败: {str(e)}")
        return False

def test_list_tasks():
    """测试任务列表"""
    print(f"\n📋 测试任务列表...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/tasks", timeout=5)
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 获取任务列表成功")
            print(f"   总任务数: {len(tasks.get('tasks', []))}")
            
            for task in tasks.get('tasks', [])[:3]:  # 显示前3个任务
                print(f"   - {task['task_id'][:8]}... | {task['status']} | {task['destination']}")
            
            return True
        else:
            print(f"❌ 获取任务列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 LangGraph多智能体AI旅行规划系统 - Web版本测试")
    print("=" * 60)
    
    # 1. 测试API健康状态
    if not test_api_health():
        return
    
    # 2. 测试创建旅行规划
    task_id = test_create_travel_plan()
    if not task_id:
        return
    
    # 3. 测试规划状态监控
    result = test_planning_status(task_id)
    if not result:
        return
    
    # 4. 测试结果下载
    test_download_result(task_id)
    
    # 5. 测试任务列表
    test_list_tasks()
    
    print(f"\n🎉 所有测试完成！")
    print(f"📊 测试结果摘要:")
    print(f"   - API健康检查: ✅")
    print(f"   - 创建规划任务: ✅")
    print(f"   - 状态监控: ✅")
    print(f"   - 结果下载: ✅")
    print(f"   - 任务列表: ✅")
    
    print(f"\n🌐 现在您可以访问前端界面:")
    print(f"   前端地址: http://localhost:8501")
    print(f"   API文档: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
