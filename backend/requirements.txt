# ============================================================================
# LangGraph 多智能体 AI 旅行助手 - 依赖包配置文件
# 面向大模型技术初学者的详细说明
# ============================================================================

# ----------------------------------------------------------------------------
# 核心基础依赖（项目运行基础库）
# 这些是项目运行的基础库，提供网络请求和环境配置功能
# ----------------------------------------------------------------------------

# HTTP 请求库 - 用于与外部 API 进行通信
# 功能：发送 HTTP 请求，获取网络数据，是 Python 中最流行的网络请求库
# 使用场景：调用旅行 API、获取天气信息、酒店预订等
requests==2.32.4

# 环境变量管理库 - 安全地管理 API 密钥和配置信息
# 功能：从 .env 文件中读取环境变量，避免在代码中硬编码敏感信息
# 使用场景：存储谷歌接口密钥、数据库连接字符串等敏感信息
python-dotenv==1.1.1

# ----------------------------------------------------------------------------
# LangGraph 和 LangChain 生态系统（多智能体人工智能框架）
# 这是构建多智能体系统的核心框架，是本项目的技术基础
# ----------------------------------------------------------------------------

# LangGraph - 多智能体工作流编排框架
# 功能：创建和管理多个 AI 智能体之间的协作流程，支持复杂的决策链
# 使用场景：协调旅行规划师、预算分析师、推荐专家等多个智能体
langgraph==0.6.3

# LangChain 核心库 - 大语言模型应用开发框架
# 功能：提供与各种大语言模型交互的统一接口和工具
# 使用场景：构建对话系统、文档问答、智能助手等 AI 应用
langchain==0.3.27

# LangChain 核心组件 - 框架的基础抽象层
# 功能：定义了 LangChain 的核心概念，如可运行组件、处理链、记忆模块等
# 使用场景：所有 LangChain 应用的基础，提供统一的编程模型
langchain-core==0.3.72

# LangChain 社区扩展 - 第三方集成和工具集合
# 功能：提供与各种外部服务和工具的集成，如搜索引擎、数据库等
# 使用场景：集成外部接口、使用社区贡献的工具和连接器
langchain-community==0.3.27

# ----------------------------------------------------------------------------
# Google Gemini 人工智能集成（谷歌人工智能集成）
# 用于接入谷歌的先进大语言模型服务
# ----------------------------------------------------------------------------

# 谷歌生成式人工智能官方软件开发包
# 功能：直接调用谷歌 Gemini 模型进行文本生成、对话等
# 使用场景：使用 Gemini Pro 进行旅行建议生成、行程规划等
google-generativeai==0.8.5

# LangChain 的谷歌人工智能集成包
# 功能：将谷歌 Gemini 模型无缝集成到 LangChain 框架中
# 使用场景：在 LangChain 应用中使用 Gemini 作为语言模型
langchain-google-genai==2.0.10

# ----------------------------------------------------------------------------
# 搜索能力（实时信息搜索功能）
# 为智能体提供实时信息搜索功能
# ----------------------------------------------------------------------------

# DuckDuckGo 搜索引擎接口
# 功能：提供隐私友好的网络搜索功能，无需接口密钥
# 使用场景：搜索最新的旅行信息、景点评价、交通状况等实时数据
duckduckgo-search==8.1.1
# ddgs - DuckDuckGo 搜索 API 非官方库
# 功能：通过 DuckDuckGo 搜索引擎获取网页、图片、新闻等信息
# 适合初学者：无需注册 API Key，简单易用，适合快速集成搜索能力
# 使用场景：智能体自动检索信息、辅助内容生成、知识查询等
ddgs==9.5.1

# ----------------------------------------------------------------------------
# 数据处理和工具库（数据验证与异步处理工具）
# 用于数据验证、类型检查和异步处理
# ----------------------------------------------------------------------------

# 数据验证和序列化库 - 现代 Python 的数据建模工具
# 功能：定义数据模型、验证输入数据、自动生成数据结构规范
# 使用场景：定义旅行计划数据结构、验证用户输入、接口数据校验
pydantic==2.11.7

# 类型扩展库 - 提供额外的类型注解支持
# 功能：为旧版本 Python 提供新的类型注解功能
# 使用场景：增强代码的类型安全性，提高开发体验
typing-extensions==4.14.1

# 异步请求限流库 - 控制并发请求频率
# 功能：限制异步操作的执行频率，避免超出接口调用限制
# 使用场景：控制对外部接口的调用频率，遵守服务商的限流规则
asyncio-throttle==1.0.2

# ----------------------------------------------------------------------------
# 增强功能库（网页解析与数据分析工具）
# 提供网页解析、XML 处理和数据分析能力
# ----------------------------------------------------------------------------

# 网页内容解析库 - HTML/XML 解析和处理
# 功能：从网页中提取结构化数据，清理和处理 HTML 内容
# 使用场景：解析旅行网站信息、提取景点详情、处理搜索结果
beautifulsoup4==4.13.4

# XML 和 HTML 处理引擎 - 高性能的解析库
# 功能：提供快速的网页标记语言解析能力，支持路径查询
# 使用场景：处理复杂的网页结构、解析标记语言格式的接口响应
lxml==6.0.0

# 数据分析库 - Python 数据科学的核心工具
# 功能：数据清洗、分析、统计计算和可视化
# 使用场景：分析旅行数据、生成统计报告、处理价格趋势等
pandas==2.3.1

# ----------------------------------------------------------------------------
# 开发和测试工具（代码测试与质量保证工具）
# 用于代码测试和质量保证
# ----------------------------------------------------------------------------

# Python 测试框架 - 现代化的单元测试工具
# 功能：编写和运行测试用例，确保代码质量和功能正确性
# 使用场景：测试智能体功能、验证接口集成、回归测试
pytest==8.4.1

# 异步测试支持 - pytest 的异步功能扩展
# 功能：支持测试异步函数和协程，适配现代异步 Python 应用
# 使用场景：测试异步的智能体交互、并发的接口调用等
pytest-asyncio==1.1.0

# ----------------------------------------------------------------------------
# 文件上传与表单处理相关依赖
# ----------------------------------------------------------------------------

# python-multipart - 处理多部分表单数据（如文件上传）
# 功能：支持 HTTP 协议中的 multipart/form-data 格式，常用于文件上传接口
# 适合初学者：与 FastAPI 集成简单，官方推荐
# 使用场景：实现图片、文档等文件上传功能
python-multipart==0.0.20

# FastAPI - 现代、快速的 Web API 框架
# 功能：用于构建高性能的 RESTful API，支持自动生成文档（Swagger UI）
# 适合初学者：语法简洁，类型提示友好，易于上手，社区活跃
# 使用场景：开发智能体后端服务、API接口、微服务等
fastapi==0.116.1

# Uvicorn - 高性能异步 Web 服务器
# 功能：作为 FastAPI 的推荐运行环境，支持异步处理，性能优异
# 适合初学者：启动命令简单，支持热重载，开发调试方便
# 使用场景：本地开发、生产环境部署 FastAPI 应用
uvicorn[standard]==0.35.0



