# HTTP 请求库 - 用于与外部 API 进行通信
# 功能：发送 HTTP 请求，获取网络数据，是 Python 中最流行的网络请求库
# 使用场景：调用旅行 API、获取天气信息、酒店预订等
requests==2.32.4

# Streamlit - 用于构建 Web 应用的 Python 框架
# 功能：快速构建交互式 Web 应用，支持数据可视化、用户界面设计等
# 适合初学者：语法简洁，上手容易，适合快速开发原型
# 使用场景：构建旅行规划器、数据分析工具、智能助手等
streamlit==1.48.0

# 数据分析库 - Python 数据科学的核心工具
# 功能：数据清洗、分析、统计计算和可视化
# 使用场景：分析旅行数据、生成统计报告、处理价格趋势等
pandas==2.3.1


# plotly - 数据可视化库
# 功能：提供强大的交互式数据可视化能力，支持折线图、柱状图、地图等多种图表类型
# 使用场景：展示旅行路线、价格趋势、用户偏好分析等可视化数据
plotly==6.2.0