# LangGraph多智能体AI旅行规划系统
## ASCII架构图

```
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                        🚀 LANGGRAPH AI旅行助手系统                                      │
│                     由Google Gemini Flash-2.0和DuckDuckGo驱动                          │
└─────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                                    🎯 系统入口点                                        │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│  main.py                    langgraph_main.py              test_langgraph_system.py     │
│  ┌─────────────┐           ┌─────────────────┐            ┌─────────────────────┐       │
│  │ 用户选择    │──────────▶│ LangGraph入口   │            │ 测试与验证           │       │
│  │ 1,2,3模式   │           │ 点（选项3）     │             │ 无需API密钥          │       │
│  │ 选择        │           │                 │            │                     │       │
│  └─────────────┘           └─────────────────┘            └─────────────────────┘       │
└─────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                              🧠 核心LANGGRAPH架构                                       │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                         │
│  ┌──────────────────────────────────────────────────────────────────────────────────┐   │
│  │                           📊 旅行计划状态                                         │   │
│  │  ┌─────────────────────────────────────────────────────────────────────────────┐ │   │
│  │  │ • messages: List[HumanMessage|AIMessage|SystemMessage]                      │ │   │
│  │  │ • destination: str                                                          │ │   │
│  │  │ • duration: int                                                             │ │   │
│  │  │ • budget_range: str                                                         │ │   │
│  │  │ • interests: List[str]                                                      │ │   │
│  │  │ • group_size: int                                                           │ │   │
│  │  │ • travel_dates: str                                                         │ │   │
│  │  │ • current_agent: str                                                        │ │   │
│  │  │ • agent_outputs: Dict[str, Any]                                             │ │   │
│  │  │ • final_plan: Dict[str, Any]                                                │ │   │
│  │  │ • iteration_count: int                                                      │ │   │
│  │  └─────────────────────────────────────────────────────────────────────────────┘ │   │
│  └──────────────────────────────────────────────────────────────────────────────────┘   │
│                                           │                                             │
│                                           ▼                                             │
│  ┌──────────────────────────────────────────────────────────────────────────────────┐   │
│  │                          🎯 协调员智能体                                          │   │
│  │                         （入口点和编排器）                                        │   │
│  │  ┌─────────────────────────────────────────────────────────────────────────────┐ │   │
│  │  │ • 分析旅行请求                                                              │ │   │
│  │  │ • 路由到专业智能体                                                          │ │   │
│  │  │ • 管理工作流状态                                                            │ │   │
│  │  │ • 综合最终建议                                                              │ │   │
│  │  │ • 控制迭代和完成                                                            │ │   │
│  │  └─────────────────────────────────────────────────────────────────────────────┘ │   │
│  └──────────────────────────────────────────────────────────────────────────────────┘   │
│                                           │                                             │
│                         ┌─────────────────┴─────────────────┐                           │
│                         ▼                                   ▼                           │
│  ┌──────────────────────────────────────┐    ┌──────────────────────────────────────┐   │
│  │        📈 智能体路由                 │    │         🔧 工具执行                  │   │
│  │    （条件边缘）                      │    │                                      │   │
│  │                                      │    │  ┌─────────────────────────────────┐ │   │
│  │ 协调员基于以下因素决策：             │    │  │  7个DuckDuckGo搜索工具          │ │   │
│  │ • 当前状态                           │    │  │  • search_destination_info      │ │   │
│  │ • 智能体完成状态                     │    │  │  • search_weather_info          │ │   │
│  │ • 信息缺口                           │    │  │  • search_attractions           │ │   │
│  │ • 工具执行需求                       │    │  │  • search_hotels                │ │   │
│  │                                      │    │  │  • search_restaurants           │ │   │
│  │ 路由到：                             │    │  │  • search_local_tips            │ │   │
│  │ ✈️ 旅行顾问                          │    │  │  • search_budget_info           │ │   │
│  │ 🌤️ 天气分析师                        │    │  └─────────────────────────────────┘ │   │
│  │ 💰 预算优化师                        │    │                                      │   │
│  │ 🏠 当地专家                          │    │  结果反馈给协调员                    │   │
│  │ 📅 行程规划师                        │    │                                      │   │
│  │ 🔧 工具                              │    │                                      │   │
│  │ 🏁 结束                              │    │                                      │   │
│  └──────────────────────────────────────┘    └──────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                              🤖 专业智能体网络                                          │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                         │
│ ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│ │   ✈️ 旅行       │  │  🌤️ 天气        │  │  💰 预算        │  │  🏠 当地        │      │
│ │   顾问          │  │   分析师        │  │   优化师        │  │   专家          │      │
│ │                 │  │                 │  │                 │  │                 │      │
│ │ • 目的地        │  │ • 天气          │  │ • 成本分析      │  │ • 内部贴士      │      │
│ │   专业知识      │  │   情报          │  │ • 预算          │  │ • 当地文化      │      │
│ │ • 景点          │  │ • 气候数据      │  │   优化          │  │ • 小众景点      │      │
│ │ • 文化          │  │ • 季节性        │  │ • 价格          │  │ • 安全信息      │      │
│ │   洞察          │  │   规划          │  │   比较          │  │ • 交通          │      │
│ │ • 最佳          │  │ • 活动          │  │ • 价值          │  │ • 当地活动      │      │
│ │   实践          │  │   推荐          │  │   推荐          │  │ • 礼仪          │      │
│ └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
│         │                     │                     │                     │              │
│         └─────────────────────┼─────────────────────┼─────────────────────┘              │
│                               │                     │                                    │
│                    ┌─────────────────┐              │                                    │
│                    │  📅 行程        │              │                                    │
│                    │   规划师        │              │                                    │
│                    │                 │              │                                    │
│                    │ • 日程          │              │                                    │
│                    │   优化          │              │                                    │
│                    │ • 物流          │              │                                    │
│                    │ • 时间          │              │                                    │
│                    │   管理          │              │                                    │
│                    │ • 活动          │              │                                    │
│                    │   排序          │              │                                    │
│                    │ • 路线          │              │                                    │
│                    │   规划          │              │                                    │
│                    └─────────────────┘              │                                    │
│                               │                     │                                    │
│                               └─────────────────────┘                                    │
│                                                                                           │
│           每个智能体都可以：                                                              │
│           • 请求工具执行                                                                  │
│           • 返回协调员                                                                    │
│           • 完成并结束工作流                                                              │
│                                                                                           │
└─────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                              🛠️ 技术栈集成                                             │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                         │
│  ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐          │
│  │   🧠 GOOGLE GEMINI   │    │  🔍 DUCKDUCKGO API  │    │  🕸️ LANGGRAPH       │         │
│  │   FLASH-2.0          │    │                     │    │   框架              │         │
│  │                     │    │                     │    │                     │          │
│  │ • ChatGoogleGenAI   │    │ • 实时搜索          │    │ • StateGraph        │          │
│  │ • 温度: 0.7         │    │ • 7个专业           │    │ • 条件边缘          │          │
│  │ • 最大令牌: 4000    │    │   搜索功能          │    │ • 消息处理          │          │
│  │ • Top-p: 0.9        │    │ • 区域: wt-wt       │    │ • 状态管理          │          │
│  │ • 高级推理          │    │ • 安全搜索          │    │ • 工作流控制        │          │
│  │ • 多轮对话          │    │ • 错误处理          │    │ • 智能体编排        │          │
│  └─────────────────────┘    └─────────────────────┘    └─────────────────────┘          │
│           │                            │                            │                    │
│           └────────────────────────────┼────────────────────────────┘                    │
│                                        │                                                 │
│  ┌─────────────────────────────────────┴─────────────────────────────────────┐          │
│  │                        📦 LANGCHAIN核心                                    │          │
│  │                                                                             │          │
│  │  • 工具装饰器 (@tool)                                                      │          │
│  │  • 消息类型 (HumanMessage, AIMessage, SystemMessage)                      │          │
│  │  • 工具执行和验证                                                          │          │
│  │  • LLM集成和响应处理                                                       │          │
│  │  • 错误处理和重试机制                                                      │          │
│  └─────────────────────────────────────────────────────────────────────────────┘          │
└─────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                                📁 系统配置                                               │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                           │
│  config/langgraph_config.py                                                              │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐ │
│  │ • GEMINI_API_KEY环境变量管理                                                         │ │
│  │ • 模型配置 (gemini-2.0-flash-exp)                                                   │ │
│  │ • 温度、令牌和AI参数                                                                 │ │
│  │ • DuckDuckGo搜索设置                                                                 │ │
│  │ • 超时和重试配置                                                                     │ │
│  │ • 区域和安全设置                                                                     │ │
│  └─────────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                           │
│  tools/__init__.py                                                                       │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐ │
│  │ • 工具注册和导出                                                                     │ │
│  │ • 工具可用性验证                                                                     │ │
│  │ • 导入和初始化处理                                                                   │ │
│  └─────────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                           │
└─────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                               🔄 工作流执行模式                                          │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                           │
│    用户输入                                                                               │
│         │                                                                                 │
│         ▼                                                                                 │
│    ┌─────────────┐                                                                        │
│    │ 初始化      │                                                                        │
│    │ 旅行请求    │                                                                        │
│    │ 状态        │                                                                        │
│    └─────────────┘                                                                        │
│         │                                                                                 │
│         ▼                                                                                 │
│    ┌─────────────┐         ┌──────────────┐         ┌─────────────┐                     │
│    │ 协调员      │──────▶  │ 路由         │──────▶  │ 专业        │                     │
│    │ 分析        │         │ 决策         │         │ 智能体      │                     │
│    └─────────────┘         └──────────────┘         └─────────────┘                     │
│         ▲                         │                         │                           │
│         │                         ▼                         ▼                           │
│    ┌─────────────┐         ┌──────────────┐         ┌─────────────┐                     │
│    │ 综合        │◀─────── │ 工具         │◀────────│ 工具        │                     │
│    │ 结果        │         │ 执行         │         │ 请求        │                     │
│    └─────────────┘         └──────────────┘         └─────────────┘                     │
│         │                                                                                 │
│         ▼                                                                                 │
│    ┌─────────────┐                                                                        │
│    │ 最终计划    │                                                                        │
│    │ 交付        │                                                                        │
│    └─────────────┘                                                                        │
│                                                                                           │
│  循环继续直到：                                                                           │
│  • 所有智能体都提供了输入                                                                 │
│  • 信息缺口被填补                                                                         │
│  • 协调员确定完成                                                                         │
│  • 达到最大迭代次数                                                                       │
│                                                                                           │
└─────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                               🎯 关键架构特性                                            │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                           │
│  ✅ 状态驱动架构                                                                         │
│     • 所有智能体交互中的持久状态                                                         │
│     • 使用TypedDict的类型安全状态管理                                                   │
│     • 消息历史和上下文保存                                                               │
│                                                                                           │
│  ✅ 条件工作流路由                                                                       │
│     • 基于当前需求的动态智能体选择                                                       │
│     • 智能工具执行决策                                                                   │
│     • 完成检测和工作流终止                                                               │
│                                                                                           │
│  ✅ 实时数据集成                                                                         │
│     • 来自DuckDuckGo的实时搜索结果                                                       │
│     • 当前天气和价格信息                                                                 │
│     • 最新的景点和活动数据                                                               │
│                                                                                           │
│  ✅ 高级LLM能力                                                                          │
│     • 所有AI推理使用Google Gemini Flash-2.0                                              │
│     • 多轮对话支持                                                                       │
│     • 具有记忆的上下文感知响应                                                           │
│                                                                                           │
│  ✅ 可扩展智能体架构                                                                     │
│     • 易于扩展的模块化智能体设计                                                         │
│     • 专业化的专业领域                                                                   │
│     • 协调的多智能体协作                                                                 │
│                                                                                           │
│  ✅ 健壮的错误处理                                                                       │
│     • 工具执行失败恢复                                                                   │
│     • API超时和重试机制                                                                  │
│     • 优雅降级到离线知识                                                                 │
│                                                                                           │
└─────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                                  📊 系统指标                                             │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                           │
│  🎯 智能体: 6个专业智能体 + 1个协调员                                                    │
│  🔧 工具: 7个DuckDuckGo搜索工具，提供实时数据                                            │
│  🧠 AI模型: Google Gemini Flash-2.0（最新一代）                                          │
│  📊 状态字段: 10个类型化状态管理字段                                                     │
│  🔀 工作流边缘: 具有7个决策点的条件路由                                                  │
│  🌐 搜索能力: 全球实时信息检索                                                           │
│  💾 内存: 完整的对话历史和上下文保存                                                     │
│  🔄 执行: 异步多智能体协作                                                               │
│                                                                                           │
└─────────────────────────────────────────────────────────────────────────────────────────┘
```

## 系统概述

LangGraph多智能体AI旅行规划系统代表了协作AI智能体的最先进实现，它们共同工作来创建全面的旅行计划。该系统基于LangGraph框架构建，集成了Google Gemini Flash-2.0和DuckDuckGo搜索，通过协调的多智能体工作流提供实时、智能的旅行规划。

### 核心组件

1. **LangGraph StateGraph**: 管理工作流编排和状态持久化
2. **协调员智能体**: 中央编排器，路由请求并综合结果
3. **专业智能体**: 5个具有特定旅行规划专业知识的领域专家智能体
4. **实时工具**: 7个DuckDuckGo搜索工具，提供当前信息
5. **Google Gemini集成**: 使用最新Gemini模型的高级AI推理
6. **状态管理**: 跨所有智能体交互的全面状态跟踪

### 工作流架构

系统采用轮辐式架构，协调员智能体位于中心，根据当前状态和信息需求智能地将请求路由到专业智能体。每个智能体都可以请求工具执行以获取实时数据，或将结果返回给协调员进行综合。

### 关键创新

- **动态路由**: 根据当前规划状态适应工作流的条件边缘
- **实时集成**: 来自DuckDuckGo搜索的跨多个旅行领域的实时数据
- **状态持久化**: 在整个工作流中维护完整的对话和规划上下文
- **多模态规划**: 将AI推理与当前市场数据和条件相结合
- **可扩展设计**: 支持轻松添加新智能体和工具的模块化架构

这种架构通过多个专业AI智能体的协调智能确保全面、及时和个性化的旅行规划。
