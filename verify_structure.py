#!/usr/bin/env python3
"""
项目结构验证脚本

验证清理后的项目结构是否正确，所有必要的文件是否存在。
"""

import os
import sys
from pathlib import Path

def check_file_exists(filepath, description=""):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {filepath} - {description}")
        return True
    else:
        print(f"❌ {filepath} - {description} (缺失)")
        return False

def check_directory_structure():
    """检查目录结构"""
    print("🔍 验证项目结构...")
    print("=" * 50)
    
    # 根目录文件
    root_files = [
        ("README.md", "主要项目说明"),
        ("README_WEB.md", "Web版本详细说明"),
        ("QUICK_START.md", "快速开始指南"),
        ("DEPLOYMENT_GUIDE.md", "部署指南"),
        ("docker-compose.yml", "Docker编排配置"),
        ("start_backend.sh", "后端启动脚本"),
        ("start_frontend.sh", "前端启动脚本"),
        ("demo.py", "演示脚本"),
        ("test_web_system.py", "Web系统测试脚本"),
        ("PROJECT_CLEANUP_SUMMARY.md", "项目清理总结")
    ]
    
    print("\n📁 根目录文件:")
    for filename, desc in root_files:
        check_file_exists(filename, desc)
    
    # 前端文件
    frontend_files = [
        ("frontend/streamlit_app.py", "Streamlit主应用"),
        ("frontend/requirements.txt", "前端依赖"),
        ("frontend/Dockerfile", "前端Docker配置")
    ]
    
    print("\n🌐 前端文件:")
    for filepath, desc in frontend_files:
        check_file_exists(filepath, desc)
    
    # 后端文件
    backend_files = [
        ("backend/api_server.py", "FastAPI服务器"),
        ("backend/requirements.txt", "后端依赖"),
        ("backend/Dockerfile", "后端Docker配置")
    ]
    
    print("\n⚡ 后端文件:")
    for filepath, desc in backend_files:
        check_file_exists(filepath, desc)
    
    # 后端模块
    backend_modules = [
        ("backend/agents/__init__.py", "智能体模块初始化"),
        ("backend/agents/langgraph_agents.py", "LangGraph智能体"),
        ("backend/config/__init__.py", "配置模块初始化"),
        ("backend/config/langgraph_config.py", "LangGraph配置"),
        ("backend/modules/__init__.py", "功能模块初始化"),
        ("backend/modules/user_input.py", "用户输入处理"),
        ("backend/tools/__init__.py", "工具模块初始化"),
        ("backend/tools/travel_tools.py", "旅行工具"),
        ("backend/utils/__init__.py", "工具函数初始化"),
        ("backend/utils/helpers.py", "辅助函数")
    ]
    
    print("\n🔧 后端模块:")
    for filepath, desc in backend_modules:
        check_file_exists(filepath, desc)
    
    # 检查目录
    directories = [
        ("frontend", "前端目录"),
        ("backend", "后端目录"),
        ("results", "结果存储目录"),
        ("backend/agents", "智能体目录"),
        ("backend/config", "配置目录"),
        ("backend/modules", "功能模块目录"),
        ("backend/tools", "工具目录"),
        ("backend/utils", "工具函数目录")
    ]
    
    print("\n📂 目录结构:")
    for dirpath, desc in directories:
        if os.path.isdir(dirpath):
            print(f"✅ {dirpath}/ - {desc}")
        else:
            print(f"❌ {dirpath}/ - {desc} (缺失)")

def check_removed_files():
    """检查已删除的文件"""
    print("\n🗑️  验证已删除的文件:")
    print("=" * 30)
    
    removed_files = [
        "agents",
        "config", 
        "modules",
        "tools",
        "utils",
        "data",
        "tests",
        "langgraph_main.py",
        "main.py",
        "multi_agent_main.py",
        "test_langgraph_system.py",
        "requirements.txt",
        "__pycache__"
    ]
    
    for item in removed_files:
        if not os.path.exists(item):
            print(f"✅ {item} - 已正确删除")
        else:
            print(f"⚠️  {item} - 仍然存在 (可能需要手动删除)")

def check_executable_permissions():
    """检查可执行文件权限"""
    print("\n🔐 验证可执行权限:")
    print("=" * 25)
    
    executable_files = [
        "start_backend.sh",
        "start_frontend.sh"
    ]
    
    for filename in executable_files:
        if os.path.exists(filename):
            if os.access(filename, os.X_OK):
                print(f"✅ {filename} - 有执行权限")
            else:
                print(f"⚠️  {filename} - 缺少执行权限 (运行: chmod +x {filename})")
        else:
            print(f"❌ {filename} - 文件不存在")

def generate_structure_tree():
    """生成项目结构树"""
    print("\n🌳 项目结构树:")
    print("=" * 20)
    
    def print_tree(directory, prefix="", max_depth=3, current_depth=0):
        if current_depth >= max_depth:
            return
            
        items = []
        try:
            items = sorted(os.listdir(directory))
        except PermissionError:
            return
            
        # 过滤隐藏文件和特定目录
        items = [item for item in items if not item.startswith('.') and item != '__pycache__']
        
        for i, item in enumerate(items):
            path = os.path.join(directory, item)
            is_last = i == len(items) - 1
            
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item}")
            
            if os.path.isdir(path) and current_depth < max_depth - 1:
                extension = "    " if is_last else "│   "
                print_tree(path, prefix + extension, max_depth, current_depth + 1)
    
    print_tree(".")

def main():
    """主验证函数"""
    print("🔍 LangGraph多智能体AI旅行规划系统 - 项目结构验证")
    print("=" * 60)
    
    # 检查目录结构
    check_directory_structure()
    
    # 检查已删除的文件
    check_removed_files()
    
    # 检查可执行权限
    check_executable_permissions()
    
    # 生成结构树
    generate_structure_tree()
    
    print("\n" + "=" * 60)
    print("✅ 项目结构验证完成!")
    print("\n💡 下一步:")
    print("1. 设置API密钥: echo 'GEMINI_API_KEY=your_key' > .env")
    print("2. 启动后端: ./start_backend.sh")
    print("3. 启动前端: ./start_frontend.sh")
    print("4. 访问应用: http://localhost:8501")

if __name__ == "__main__":
    main()
